
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import {
  cn,
  debounce,
  throttle,
  formatDate,
  formatCurrency,
  capitalize,
  generateId,
  deepClone,
  isValidEmail,
  getInitials,
  truncateText
} from '../index'

describe('Utility Functions', () => {
  describe('cn (className utility)', () => {
    it('merges classes correctly', () => {
      expect(cn('bg-red-500', 'text-white')).toContain('bg-red-500')
      expect(cn('bg-red-500', 'text-white')).toContain('text-white')
    })

    it('handles conditional classes', () => {
      const isActive = true
      const result = cn('base-class', isActive && 'active-class')
      expect(result).toContain('base-class')
      expect(result).toContain('active-class')
    })

    it('handles undefined and null values', () => {
      const result = cn('base-class', undefined, null, 'another-class')
      expect(result).toContain('base-class')
      expect(result).toContain('another-class')
    })
  })

  describe('debounce', () => {
    beforeEach(() => {
      vi.useFakeTimers()
    })

    afterEach(() => {
      vi.useRealTimers()
    })

    it('delays function execution', () => {
      const func = vi.fn()
      const debouncedFunc = debounce(func, 100)

      debouncedFunc()
      expect(func).not.toHaveBeenCalled()

      vi.advanceTimersByTime(100)
      expect(func).toHaveBeenCalledTimes(1)
    })

    it('cancels previous calls', () => {
      const func = vi.fn()
      const debouncedFunc = debounce(func, 100)

      debouncedFunc()
      debouncedFunc()
      debouncedFunc()

      vi.advanceTimersByTime(100)
      expect(func).toHaveBeenCalledTimes(1)
    })
  })

  describe('throttle', () => {
    beforeEach(() => {
      vi.useFakeTimers()
    })

    afterEach(() => {
      vi.useRealTimers()
    })

    it('limits function execution frequency', () => {
      const func = vi.fn()
      const throttledFunc = throttle(func, 100)

      throttledFunc()
      throttledFunc()
      throttledFunc()

      expect(func).toHaveBeenCalledTimes(1)

      vi.advanceTimersByTime(100)
      throttledFunc()
      expect(func).toHaveBeenCalledTimes(2)
    })
  })

  describe('formatDate', () => {
    it('formats date correctly', () => {
      const date = new Date('2024-01-15')
      const formatted = formatDate(date, 'en-US')
      expect(formatted).toMatch(/1\/14\/2024/)
    })

    it('handles string dates', () => {
      const formatted = formatDate('2024-01-15', 'en-US')
      expect(formatted).toMatch(/1\/14\/2024/)
    })
  })

  describe('formatCurrency', () => {
    it('formats currency correctly', () => {
      const formatted = formatCurrency(1234.56, 'USD', 'en-US')
      expect(formatted).toBe('$1,234.56')
    })

    it('handles different currencies', () => {
      const formatted = formatCurrency(1234.56, 'EUR', 'en-US')
      expect(formatted).toContain('€')
    })
  })

  describe('capitalize', () => {
    it('capitalizes first letter', () => {
      expect(capitalize('hello')).toBe('Hello')
      expect(capitalize('WORLD')).toBe('WORLD')
      expect(capitalize('test string')).toBe('Test string')
    })

    it('handles empty string', () => {
      expect(capitalize('')).toBe('')
    })
  })

  describe('generateId', () => {
    it('generates id of default length', () => {
      const id = generateId()
      expect(id).toHaveLength(8)
      expect(typeof id).toBe('string')
    })

    it('generates id of specified length', () => {
      const id = generateId(12)
      expect(id).toHaveLength(12)
    })

    it('generates unique ids', () => {
      const id1 = generateId()
      const id2 = generateId()
      expect(id1).not.toBe(id2)
    })
  })

  describe('deepClone', () => {
    it('clones simple objects', () => {
      const obj = { a: 1, b: 'test' }
      const cloned = deepClone(obj)

      expect(cloned).toEqual(obj)
      expect(cloned).not.toBe(obj)
    })

    it('clones nested objects', () => {
      const obj = { a: { b: { c: 1 } } }
      const cloned = deepClone(obj)

      expect(cloned).toEqual(obj)
      expect(cloned.a).not.toBe(obj.a)
      expect(cloned.a.b).not.toBe(obj.a.b)
    })

    it('clones arrays', () => {
      const arr = [1, { a: 2 }, [3, 4]]
      const cloned = deepClone(arr)

      expect(cloned).toEqual(arr)
      expect(cloned).not.toBe(arr)
      expect(cloned[1]).not.toBe(arr[1])
    })

    it('handles null and undefined', () => {
      expect(deepClone(null)).toBe(null)
      expect(deepClone(undefined)).toBe(undefined)
    })
  })

  describe('isValidEmail', () => {
    it('validates correct email addresses', () => {
      expect(isValidEmail('<EMAIL>')).toBe(true)
      expect(isValidEmail('<EMAIL>')).toBe(true)
    })

    it('rejects invalid email addresses', () => {
      expect(isValidEmail('invalid-email')).toBe(false)
      expect(isValidEmail('@domain.com')).toBe(false)
      expect(isValidEmail('user@')).toBe(false)
    })
  })

  describe('getInitials', () => {
    it('gets initials from name', () => {
      expect(getInitials('John Doe')).toBe('JD')
      expect(getInitials('Alice Bob Charlie')).toBe('AB')
      expect(getInitials('SingleName')).toBe('S')
    })

    it('handles empty or whitespace names', () => {
      expect(getInitials('')).toBe('')
      expect(getInitials('   ')).toBe('')
    })
  })

  describe('truncateText', () => {
    it('truncates long text', () => {
      const text = 'This is a very long text that should be truncated'
      const truncated = truncateText(text, 20)

      expect(truncated).toHaveLength(23) // 20 + '...'
      expect(truncated.endsWith('...')).toBe(true)
    })

    it('does not truncate short text', () => {
      const text = 'Short text'
      const result = truncateText(text, 20)

      expect(result).toBe(text)
      expect(result.endsWith('...')).toBe(false)
    })
  })
})
