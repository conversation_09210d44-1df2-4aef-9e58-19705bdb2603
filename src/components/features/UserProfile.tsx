import { useState } from "react";
import { useTranslation } from "react-i18next";
import { User, Mail, Edit, Save, X } from "lucide-react";
import { useAppDispatch, useAppSelector } from "@/hooks/redux";
import { loginSuccess, updateUser } from "@/store/slices/userSlice";
import { useNotification } from "@/hooks/useNotification";
import { Button } from "@/components/ui";
import { Input } from "@/components/ui";
import { Card } from "@/components/ui";
import { Modal } from "@/components/ui";
import { generateId } from "@/utils";

const UserProfile = () => {
  const { t } = useTranslation("common");
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state) => state.user);
  const notification = useNotification();

  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editForm, setEditForm] = useState({
    name: user?.name || "",
    email: user?.email || "",
  });

  // Demo function to create a user if none exists
  const createDemoUser = () => {
    const demoUser = {
      id: generateId(),
      name: "Demo User",
      email: "<EMAIL>",
      preferences: {
        theme: "system" as const,
        language: "en" as const,
        notifications: true,
      },
    };

    dispatch(loginSuccess(demoUser));
    notification.success(t("notifications.success"), "Demo user created!");
  };

  const handleSaveProfile = () => {
    if (!user) return;

    const updatedUser = {
      ...user,
      name: editForm.name,
      email: editForm.email,
    };

    dispatch(updateUser(updatedUser));
    setIsEditModalOpen(false);
    notification.success(t("notifications.profileUpdated"));
  };

  const handleEditFormChange = (field: string, value: string) => {
    setEditForm((prev) => ({ ...prev, [field]: value }));
  };

  const openEditModal = () => {
    if (user) {
      setEditForm({
        name: user.name,
        email: user.email,
      });
      setIsEditModalOpen(true);
    }
  };

  if (!user) {
    return (
      <Card className="text-center">
        <div className="py-8">
          <User size={48} className="mx-auto text-secondary-400 mb-4" />
          <h3 className="text-lg font-medium text-secondary-900 mb-2">
            No User Profile
          </h3>
          <p className="text-secondary-600 mb-6">
            Create a demo user to explore the profile features
          </p>
          <Button onClick={createDemoUser}>Create Demo User</Button>
        </div>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <div className="text-center mb-6">
          <div className="inline-flex items-center justify-center w-20 h-20 bg-primary-100 text-primary-600 rounded-full text-2xl font-bold mb-4">
            {user.name.charAt(0).toUpperCase()}
          </div>
          <h3 className="text-xl font-semibold text-secondary-900">
            {user.name}
          </h3>
          <p className="text-secondary-600">{user.email}</p>
        </div>

        <div className="space-y-4">
          <div className="flex items-center space-x-3 p-3 bg-secondary-50 rounded-md">
            <User size={20} className="text-secondary-500" />
            <div>
              <div className="text-sm font-medium text-secondary-700">
                {t("user.name")}
              </div>
              <div className="text-secondary-600">{user.name}</div>
            </div>
          </div>

          <div className="flex items-center space-x-3 p-3 bg-secondary-50 rounded-md">
            <Mail size={20} className="text-secondary-500" />
            <div>
              <div className="text-sm font-medium text-secondary-700">
                {t("user.email")}
              </div>
              <div className="text-secondary-600">{user.email}</div>
            </div>
          </div>
        </div>

        <div className="mt-6">
          <Button
            onClick={openEditModal}
            className="flex items-center space-x-2 w-full justify-center"
          >
            <Edit size={16} />
            <span>{t("user.updateProfile")}</span>
          </Button>
        </div>
      </Card>

      {/* Edit Profile Modal */}
      <Modal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        title={t("user.updateProfile")}
      >
        <div className="space-y-4">
          <Input
            label={t("user.name")}
            value={editForm.name}
            onChange={(e) => handleEditFormChange("name", e.target.value)}
            placeholder={t("forms.placeholders.name")}
            required
          />

          <Input
            label={t("user.email")}
            type="email"
            value={editForm.email}
            onChange={(e) => handleEditFormChange("email", e.target.value)}
            placeholder={t("forms.placeholders.email")}
            required
          />

          <div className="flex space-x-3 pt-4">
            <Button
              variant="secondary"
              onClick={() => setIsEditModalOpen(false)}
              className="flex items-center space-x-2 flex-1"
            >
              <X size={16} />
              <span>{t("app.cancel")}</span>
            </Button>

            <Button
              onClick={handleSaveProfile}
              disabled={!editForm.name.trim() || !editForm.email.trim()}
              className="flex items-center space-x-2 flex-1"
            >
              <Save size={16} />
              <span>{t("app.save")}</span>
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default UserProfile;
