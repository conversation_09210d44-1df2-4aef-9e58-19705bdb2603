
import { useTranslation } from 'react-i18next'
import { Sun, Moon, Monitor, Bell, BellOff } from 'lucide-react'
import { useAppDispatch, useAppSelector } from '@/hooks/redux'
import { setTheme, toggleNotifications } from '@/store/slices/appSlice'
import { useNotification } from '@/hooks/useNotification'
import Button from '@/components/ui/Button'
import Card from '@/components/ui/Card'
import { cn } from '@/utils'

const SettingsPanel = () => {
  const { t } = useTranslation('common')
  const dispatch = useAppDispatch()
  const { preferences } = useAppSelector(state => state.app)
  const notification = useNotification()

  const themes = [
    { value: 'light', label: t('settings.lightMode'), icon: Sun },
    { value: 'dark', label: t('settings.darkMode'), icon: Moon },
    { value: 'system', label: t('settings.systemMode'), icon: Monitor },
  ] as const

  const handleThemeChange = (theme: typeof themes[number]['value']) => {
    dispatch(setTheme(theme))
    notification.success(t('notifications.themeChanged', { theme: t(`settings.${theme}Mode`) }))
  }

  const handleNotificationToggle = () => {
    dispatch(toggleNotifications())
    notification.info(
      t('notifications.notificationToggled', {
        status: !preferences.notifications 
          ? t('notifications.enabled')
          : t('notifications.disabled')
      })
    )
  }

  return (
    <Card>
      <h3 className="text-lg font-semibold text-secondary-900 mb-6">
        {t('navigation.settings')}
      </h3>

      {/* Theme Selection */}
      <div className="mb-6">
        <h4 className="text-sm font-medium text-secondary-700 mb-3">
          {t('settings.theme')}
        </h4>
        <div className="grid grid-cols-3 gap-2">
          {themes.map((theme) => (
            <Button
              key={theme.value}
              variant={preferences.theme === theme.value ? 'primary' : 'secondary'}
              size="sm"
              onClick={() => handleThemeChange(theme.value)}
              className={cn(
                'flex flex-col items-center space-y-1 py-3 h-auto',
                preferences.theme === theme.value && 'ring-2 ring-primary-500'
              )}
            >
              <theme.icon size={20} />
              <span className="text-xs">{theme.label}</span>
            </Button>
          ))}
        </div>
      </div>

      {/* Notifications */}
      <div>
        <h4 className="text-sm font-medium text-secondary-700 mb-3">
          {t('settings.notifications')}
        </h4>
        <Button
          variant={preferences.notifications ? 'primary' : 'secondary'}
          size="md"
          onClick={handleNotificationToggle}
          className="flex items-center space-x-2 w-full justify-center"
        >
          {preferences.notifications ? (
            <>
              <Bell size={20} />
              <span>{t('settings.disableNotifications')}</span>
            </>
          ) : (
            <>
              <BellOff size={20} />
              <span>{t('settings.enableNotifications')}</span>
            </>
          )}
        </Button>
      </div>

      {/* Current Language Display */}
      <div className="mt-6 pt-6 border-t border-secondary-200">
        <h4 className="text-sm font-medium text-secondary-700 mb-2">
          {t('language.currentLanguage')}
        </h4>
        <div className="flex items-center space-x-2 text-sm text-secondary-600">
          <span className="text-lg">
            {preferences.language === 'en' && '🇺🇸'}
            {preferences.language === 'hi' && '🇮🇳'}
            {preferences.language === 'mr' && '🇮🇳'}
          </span>
          <span>
            {preferences.language === 'en' && 'English'}
            {preferences.language === 'hi' && 'हिन्दी'}
            {preferences.language === 'mr' && 'मराठी'}
          </span>
        </div>
      </div>
    </Card>
  )
}

export default SettingsPanel
