
import { useEffect } from 'react'
import { X, CheckCircle, AlertTriangle, Info, AlertCircle } from 'lucide-react'
import { useAppDispatch, useNotifications } from '@/hooks/redux'
import { removeNotification } from '@/store/slices/notificationSlice'
import Button from '@/components/ui/Button'
import { cn } from '@/utils'

const NotificationSystem = () => {
  const dispatch = useAppDispatch()
  const notifications = useNotifications()

  const getIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <CheckCircle size={20} />
      case 'error':
        return <AlertCircle size={20} />
      case 'warning':
        return <AlertTriangle size={20} />
      default:
        return <Info size={20} />
    }
  }

  const getStyles = (type: string) => {
    switch (type) {
      case 'success':
        return 'border-green-200 bg-green-50 text-green-800'
      case 'error':
        return 'border-red-200 bg-red-50 text-red-800'
      case 'warning':
        return 'border-yellow-200 bg-yellow-50 text-yellow-800'
      default:
        return 'border-blue-200 bg-blue-50 text-blue-800'
    }
  }

  const handleRemove = (id: string) => {
    dispatch(removeNotification(id))
  }

  // Auto-remove notifications after their duration
  useEffect(() => {
    notifications.forEach((notification) => {
      if (notification.duration) {
        const timer = setTimeout(() => {
          handleRemove(notification.id)
        }, notification.duration)

        return () => clearTimeout(timer)
      }
    })
  }, [notifications])

  if (notifications.length === 0) return null

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2 max-w-sm">
      {notifications.map((notification) => (
        <div
          key={notification.id}
          className={cn(
            'border rounded-lg shadow-lg p-4 animate-slide-up',
            getStyles(notification.type)
          )}
        >
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              {getIcon(notification.type)}
            </div>
            
            <div className="flex-1 min-w-0">
              <h4 className="text-sm font-medium">
                {notification.title}
              </h4>
              <p className="text-sm mt-1 opacity-90">
                {notification.message}
              </p>
            </div>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleRemove(notification.id)}
              className="flex-shrink-0 p-1 hover:bg-white/20"
            >
              <X size={16} />
            </Button>
          </div>
        </div>
      ))}
    </div>
  )
}

export default NotificationSystem
