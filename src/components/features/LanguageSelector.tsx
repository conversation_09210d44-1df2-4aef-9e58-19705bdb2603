import { useTranslation } from "react-i18next";
import { Check } from "lucide-react";
import { useAppDispatch, useAppSelector } from "@/hooks/redux";
import { setLanguage } from "@/store/slices/appSlice";
import { useNotification } from "@/hooks/useNotification";
import { LANGUAGES } from "@/i18n";
import { <PERSON><PERSON>, Card } from "@/components/ui";
import { cn } from "@/utils";

interface LanguageSelectorProps {
  onClose?: () => void;
}

const LanguageSelector = ({ onClose }: LanguageSelectorProps) => {
  const { t, i18n } = useTranslation("common");
  const dispatch = useAppDispatch();
  const { preferences } = useAppSelector((state) => state.app);
  const notification = useNotification();

  const handleLanguageChange = async (languageCode: string) => {
    try {
      await i18n.changeLanguage(languageCode);
      dispatch(setLanguage(languageCode as "en" | "hi" | "mr"));

      const selectedLanguage = LANGUAGES.find(
        (lang) => lang.code === languageCode
      );
      notification.success(
        t("notifications.languageChanged", {
          language: selectedLanguage?.nativeName || languageCode,
        })
      );

      onClose?.();
    } catch (error) {
      notification.error(t("notifications.error"));
    }
  };

  return (
    <Card padding="sm" shadow="lg" className="min-w-48">
      <div className="space-y-1">
        <h3 className="font-medium text-sm text-secondary-700 px-3 py-2">
          {t("language.selectLanguage")}
        </h3>

        {LANGUAGES.map((language) => (
          <Button
            key={language.code}
            variant="ghost"
            size="sm"
            onClick={() => handleLanguageChange(language.code)}
            className={cn(
              "w-full justify-start space-x-3 px-3 py-2",
              preferences.language === language.code &&
                "bg-primary-50 text-primary-600"
            )}
          >
            <span className="text-lg">{language.flag}</span>
            <div className="flex-1 text-left">
              <div className="text-sm font-medium">{language.nativeName}</div>
              <div className="text-xs text-secondary-500">{language.name}</div>
            </div>
            {preferences.language === language.code && (
              <Check size={16} className="text-primary-600" />
            )}
          </Button>
        ))}
      </div>
    </Card>
  );
};

export default LanguageSelector;
