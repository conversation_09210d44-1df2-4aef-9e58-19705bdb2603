// Localization
import { useTranslation } from "react-i18next";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";

//types
import type { RootLayoutProps } from "@/types/app";

// Redux
import { useAppSelector } from "@/hooks/redux";

// Layout Components
import Header from "@/components/layout/Header";
import Sidebar from "@/components/layout/Sidebar";
import Footer from "@/components/layout/Footer";
import { LoginForm, ProtectedRoute } from "@/components/Auth";

import UserProfile from "@/components/features/UserProfile";

// Utils
import { cn } from "@/utils";

// UI Components
import { Card } from "@/components/ui";

import Home from "@/pages/Home";
import Dashboard from "@/pages/Dashboard";

const AppContent = () => {
  const { t } = useTranslation("common");
  const { sidebarOpen } = useAppSelector((state: RootLayoutProps) => state.app);

  return (
    <div className="min-h-screen bg-secondary-50 flex">
      {/* Sidebar */}
      <Sidebar />

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <Header />

        {/* Main Content Area */}
        <main
          className={cn(
            "flex-1 p-6 transition-all duration-300",
            sidebarOpen ? "md:ml-0" : "md:ml-0"
          )}
        >
          {/* Welcome Section */}
          {/* {<div className="mb-8">
            <Card padding="lg" className="text-center">
              <h1 className="text-3xl font-bold mb-2 animate-fade-in">
                {t("app.welcome")}
              </h1>
              <p className="text-lg max-w-2xl mx-auto animate-fade-in">
                {t("app.description")}
              </p>
            </Card>
          </div>} */}
          <Router>
            <ProtectedRoute>
              <Routes>
                <Route path="/" element={<Home />} />
                <Route path="/dashboard" element={<Dashboard />} />
              </Routes>
            </ProtectedRoute>
          </Router>
        </main>

        {/* Footer */}
        <Footer />
      </div>
    </div>
  );
};

export default AppContent;
