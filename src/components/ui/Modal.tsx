import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "@/lib";

const modalVariants = cva(
  "fixed top-0 left-0 w-full h-full flex items-center justify-center bg-black/50",
  {
    variants: {
      variant: {
        default: "bg-black/50",
        secondary: "bg-white/50",
        destructive: "bg-red-500/50",
        ghost: "bg-transparent",
        link: "bg-transparent",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
);

function Modal({
  className,
  variant,
  asChild = false,
  ...props
}: React.ComponentProps<"div"> &
  VariantProps<typeof modalVariants> & {
    asChild?: boolean;
  }) {
  const Comp = asChild ? Slot : "div";

  return (
    <Comp
      data-slot="modal"
      className={cn(modalVariants({ variant, className }))}
      {...props}
    />
  );
}

export { Modal, modalVariants };
