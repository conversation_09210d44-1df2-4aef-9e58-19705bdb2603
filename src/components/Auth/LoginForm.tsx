import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Eye, EyeOff, Mail, Lock } from "lucide-react";
import { useAppDispatch } from "@/hooks/redux";
import {
  loginStart,
  loginSuccess,
  loginFailure,
} from "@/store/slices/authSlice";
import { authenticateUser, isValidEmail, isValidPassword } from "@/lib";

// Utils
import { cn } from "@/utils";

// Styles
const containerStyles =
  "min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8";
const formWrapperStyles = "max-w-md w-full space-y-8";
const headerStyles = "text-center";
const titleStyles = "mt-6 text-3xl font-extrabold";
const subtitleStyles = "mt-2 text-sm dark";
const formStyles = "mt-8 space-y-6";
const errorStyles =
  "border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm";
const fieldsContainerStyles = "space-y-4";
const labelStyles = "block text-sm font-medium dark mb-2";
const inputWrapperStyles = "relative";
const iconStyles = "absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5";
const inputBaseStyles = [
  "pl-10 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",
];
const inputErrorBorderStyles = "border-red-300";
const inputNormalBorderStyles = "border-gray-300";
const passwordInputBaseStyles = [
  "pl-10 pr-10 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",
];
const fieldErrorStyles = "mt-1 text-sm text-red-600";
const toggleButtonStyles =
  "absolute right-3 top-1/2 transform -translate-y-1/2";
const eyeIconStyles = "h-5 w-5";
const submitButtonStyles =
  "group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounde focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed";
const demoCredentialsStyles = "mt-4 p-4 border border-blue-200 rounded-md";
const demoTitleStyles = "text-sm text-blue-800 font-medium mb-2";
const demoTextStyles = "text-xs text-blue-700 space-y-1";

const LoginForm: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();

  const [formData, setFormData] = useState({
    email: "",
    password: "",
  });
  const [showPassword, setShowPassword] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validation
    const newErrors: Record<string, string> = {};

    if (!formData.email) {
      newErrors.email = "Email is required";
    } else if (!isValidEmail(formData.email)) {
      newErrors.email = "Invalid email format";
    }

    if (!formData.password) {
      newErrors.password = "Password is required";
    } else if (!isValidPassword(formData.password)) {
      newErrors.password = "Password must be at least 6 characters";
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    dispatch(loginStart());

    // Simulate API call
    setTimeout(() => {
      const user = authenticateUser(formData.email, formData.password);

      if (user) {
        dispatch(loginSuccess(user));
        setErrors({});
      } else {
        dispatch(loginFailure());
        setErrors({ general: "Invalid email or password" });
      }
    }, 1000);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));

    // Clear field error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: "" }));
    }
  };

  return (
    <div className={containerStyles}>
      <div className={formWrapperStyles}>
        <div className={headerStyles}>
          <h2 className={titleStyles}>{t("auth.loginTitle")}</h2>
          <p className={subtitleStyles}>{t("auth.loginSubtitle")}</p>
        </div>

        <form className={formStyles} onSubmit={handleSubmit}>
          {errors.general && (
            <div className={errorStyles}>{errors.general}</div>
          )}

          <div className={fieldsContainerStyles}>
            {/* Email field */}
            <div>
              <label htmlFor="email" className={labelStyles}>
                {t("auth.email")}
              </label>
              <div className={inputWrapperStyles}>
                <Mail className={iconStyles} />
                <input
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleChange}
                  className={cn(
                    ...inputBaseStyles,
                    errors.email
                      ? inputErrorBorderStyles
                      : inputNormalBorderStyles
                  )}
                  placeholder="<EMAIL>"
                />
              </div>
              {errors.email && (
                <p className={fieldErrorStyles}>{errors.email}</p>
              )}
            </div>

            {/* Password field */}
            <div>
              <label htmlFor="password" className={labelStyles}>
                {t("auth.password")}
              </label>
              <div className={inputWrapperStyles}>
                <Lock className={iconStyles} />
                <input
                  id="password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  value={formData.password}
                  onChange={handleChange}
                  className={cn(
                    ...passwordInputBaseStyles,
                    errors.password
                      ? inputErrorBorderStyles
                      : inputNormalBorderStyles
                  )}
                  placeholder="admin123"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className={toggleButtonStyles}
                >
                  {showPassword ? (
                    <EyeOff className={eyeIconStyles} />
                  ) : (
                    <Eye className={eyeIconStyles} />
                  )}
                </button>
              </div>
              {errors.password && (
                <p className={fieldErrorStyles}>{errors.password}</p>
              )}
            </div>
          </div>

          <div>
            <button type="submit" className={submitButtonStyles}>
              {t("auth.loginButton")}
            </button>
          </div>

          {/* Demo credentials */}
          <div className={demoCredentialsStyles}>
            <p className={demoTitleStyles}>Demo Credentials:</p>
            <div className={demoTextStyles}>
              <p>Admin: <EMAIL> / admin123</p>
              <p>User: <EMAIL> / user123</p>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export { LoginForm };
