import React from "react";
import { useAppSelector } from "../../hooks/redux";
import { LoginForm } from "./LoginForm";

interface ProtectedRouteProps {
  children: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { isAuthenticated } = useAppSelector((state) => state.auth);

  if (!isAuthenticated) {
    return <LoginForm />;
  }

  return <>{children}</>;
};

export { ProtectedRoute };
