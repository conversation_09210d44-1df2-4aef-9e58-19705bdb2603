"use client";

import React from "react";
import type { ThemePickerProps, ColorPreset } from "../types";

/**
 * Default color presets
 */
const DEFAULT_PRESETS: ColorPreset[] = [
  { name: "Ocean Blue", color: "#0077be", description: "Deep ocean blue" },
  {
    name: "Forest Green",
    color: "#228B22",
    description: "Natural forest green",
  },
  {
    name: "Sunset Orange",
    color: "#FF6347",
    description: "Warm sunset orange",
  },
  { name: "Royal Purple", color: "#663399", description: "Rich royal purple" },
  { name: "Rose Pink", color: "#ff6b6b", description: "Soft rose pink" },
  { name: "Tea<PERSON>", color: "#4ecdc4", description: "Modern teal" },
];

/**
 * Reusable theme picker component
 *
 * @param props - Component props
 * @returns JSX element
 */
export function ThemePicker({
  colors,
  onColorChange,
  label = "Theme Color",
  className = "",
  showPresets = true,
  presets = DEFAULT_PRESETS,
  compact = false,
}: ThemePickerProps) {
  const handleColorChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    onColorChange(event.target.value);
  };

  const handlePresetClick = (color: string) => {
    onColorChange(color);
  };

  if (compact) {
    return (
      <div className={`theme-picker-compact ${className}`}>
        <label htmlFor="theme-color-picker" className="theme-picker-label">
          {label}
        </label>
        <input
          id="theme-color-picker"
          type="color"
          value={colors.baseColor}
          onChange={handleColorChange}
          className="color-picker"
          title={`${label} (Current: ${colors.baseColor})`}
        />
      </div>
    );
  }

  return (
    <div className={`theme-picker ${className}`}>
      <div className="theme-picker-header">
        <h3 className="theme-picker-title">{label}</h3>
        <p className="theme-picker-description">
          Pick any color and watch the interface adapt
        </p>
      </div>

      <div className="theme-picker-controls">
        <div className="color-input-group">
          <label htmlFor="theme-color-picker" className="color-input-label">
            Custom Color
          </label>
          <div className="color-input-wrapper">
            <input
              id="theme-color-picker"
              type="color"
              value={colors.baseColor}
              onChange={handleColorChange}
              className="color-picker"
              title={`Select theme color (Current: ${colors.baseColor})`}
            />
            <div className="color-info">
              <span className="color-value">{colors.baseColor}</span>
              <span className="color-lightness">
                Lightness: {Math.round(colors.lightness)}%
              </span>
              <span className="text-mode">
                Text: {colors.isDarkText ? "Dark" : "Light"}
              </span>
            </div>
          </div>
        </div>

        {showPresets && presets.length > 0 && (
          <div className="preset-colors">
            <label className="preset-label">Quick Presets</label>
            <div className="preset-grid">
              {presets.map((preset, index) => (
                <button
                  key={index}
                  onClick={() => handlePresetClick(preset.color)}
                  className={`preset-color-button ${
                    colors.baseColor.toLowerCase() ===
                    preset.color.toLowerCase()
                      ? "preset-active"
                      : ""
                  }`}
                  style={{ backgroundColor: preset.color }}
                  title={`${preset.name}${
                    preset.description ? ` - ${preset.description}` : ""
                  }`}
                  aria-label={`Apply ${preset.name} theme`}
                >
                  <span className="preset-name">{preset.name}</span>
                </button>
              ))}
            </div>
          </div>
        )}
      </div>

      <div className="theme-info-panel">
        <h4 className="theme-info-title">Current Theme</h4>
        <div className="theme-colors-preview">
          <div className="color-swatch">
            <div className="swatch bg-base" title="Base Color"></div>
            <span>Base</span>
          </div>
          <div className="color-swatch">
            <div className="swatch bg-surface" title="Surface Color"></div>
            <span>Surface</span>
          </div>
          <div className="color-swatch">
            <div className="swatch bg-brand" title="Brand Color"></div>
            <span>Brand</span>
          </div>
          <div className="color-swatch">
            <div
              className="swatch"
              style={{ backgroundColor: "var(--text-color)" }}
              title="Text Color"
            ></div>
            <span>Text</span>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ThemePicker;
