import { useTranslation } from "react-i18next";
import { Copyright } from "lucide-react";

const Footer = () => {
  const { t } = useTranslation("common");

  return (
    <footer className="border-t mt-auto">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <p className="flex items-center justify-center space-x-1 text-xs mt-2">
            <Copyright size={12} />
            <span>{new Date().getFullYear().toString()}</span>
            <span>{t("footer.copyright")}</span>
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
