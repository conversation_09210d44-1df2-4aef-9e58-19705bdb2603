import { useState } from "react";
import { useTranslation } from "react-i18next";
import { Menu, LogOut, Settings, User, Globe } from "lucide-react";
import { useAppSelector, useAppDispatch } from "@/hooks/redux";
import { toggleSidebar } from "@/store/slices/appSlice";
import { logout } from "@/store/slices/authSlice";
import { Button } from "@/components/ui";
import LanguageSelector from "@/components/features/LanguageSelector";

import { ThemePicker } from "@/components/ThemePicker";
import { useThemePicker } from "@/hooks/useThemePicker";

//types
import type { RootLayoutProps, ThemeColors } from "@/types";

const Header = () => {
  const { colors, updateTheme } = useThemePicker({
    initialColor: "#fc6c00",
    enableTransitions: true,
    colorRelations: {
      surfaceLightness: 1.2,
      brandChroma: 5,
    },
  });
  const handleThemeChange = (color: string) => {
    updateTheme(color, (newColors: ThemeColors) => {
      console.log("Theme updated:", newColors);
    });
  };
  const { t } = useTranslation("common");
  const dispatch = useAppDispatch();
  const { sidebarOpen } = useAppSelector((state: RootLayoutProps) => state.app);

  const { user } = useAppSelector((state: RootLayoutProps) => state.auth);
  const [showLanguageSelector, setShowLanguageSelector] = useState(false);

  return (
    <header className="sticky top-0 z-40 bg-surface shadow-sm">
      <div className="max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Left side */}
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => dispatch(toggleSidebar())}
              className={`md{sidebarOpen ? ":hidden" : ""}`}
            >
              {!sidebarOpen && <Menu size={20} />}
            </Button>

            <h1 className="text-2xl font-bold text-primary">
              {t("app.title")}
            </h1>
          </div>

          {/* Center - Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <a
              href="#"
              className="text-secondary-700 hover:text-primary-600 font-medium transition-colors"
            >
              {t("navigation.home")}
            </a>
            <a
              href="#"
              className="text-secondary-700 hover:text-primary-600 font-medium transition-colors"
            >
              {t("navigation.dashboard")}
            </a>
            <a
              href="#"
              className="text-secondary-700 hover:text-primary-600 font-medium transition-colors"
            >
              {t("navigation.about")}
            </a>
          </nav>

          {/* Right side */}
          <div className="flex items-center space-x-4">
            {/* Language Selector */}
            <div className="relative">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowLanguageSelector(!showLanguageSelector)}
                className="flex items-center space-x-2"
              >
                <Globe size={16} />
                <span className="hidden sm:inline">
                  {t("settings.language")}
                </span>
              </Button>

              {showLanguageSelector && (
                <div className="absolute right-0 top-full mt-2">
                  <LanguageSelector
                    onClose={() => setShowLanguageSelector(false)}
                  />
                </div>
              )}
            </div>

            {/* Settings */}
            <Button variant="ghost" size="sm">
              <Settings size={16} />
              <ThemePicker
                colors={colors}
                onColorChange={handleThemeChange}
                compact={true}
                className="header-theme-picker"
              />
              <span className="hidden sm:inline ml-2">
                {t("navigation.settings")}
              </span>
            </Button>

            {/* User Menu */}
            <div className="flex items-center space-x-2">
              {user ? (
                <div className="flex items-center space-x-3">
                  <div className="flex items-center justify-center w-8 h-8 bg-primary-100 text-primary-600 rounded-full font-medium text-sm">
                    {user.name.charAt(0).toUpperCase()}
                  </div>
                  <span className="hidden md:inline text-sm font-medium text-secondary-700">
                    {user.name}
                  </span>
                  <Button
                    variant="primary"
                    size="sm"
                    onClick={() => dispatch(logout())}
                    title={t("auth.logout")}
                  >
                    <LogOut className="h-5 w-5" />
                  </Button>
                </div>
              ) : (
                <Button variant="primary" size="sm">
                  <User size={16} />
                  <span className="ml-2">{t("navigation.login")}</span>
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
