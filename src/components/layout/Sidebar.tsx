import { useTranslation } from "react-i18next";
import { Home, BarChart3, <PERSON><PERSON>, <PERSON>ting<PERSON>, User, Info, X } from "lucide-react";
import { useAppSelector, useAppDispatch } from "@/hooks/redux";
import { setSidebarOpen, toggleSidebar } from "@/store/slices/appSlice";
import { Button } from "@/components/ui";
import { cn } from "@/utils";

//types
import type { RootLayoutProps } from "@/types";

// Sidebar styles
const sidebarStyles = {
  backdrop: "fixed inset-0 bg-opacity-50 z-40 md:hidden",
  container:
    "fixed left-0 top-0 z-50 h-full w-64 border-r shadow-lg transform transition-transform duration-300 ease-in-out md:translate-x-0 md:static md:z-auto",
  closeButton: "absolute top-4 right-4",
  header: "flex items-center justify-between px-6 py-4 border-b",
  headerTitle: "text-lg font-semibold text-secondary-900",
  menuButton: "",
  navigation: "px-4 py-6",
  navigationList: "space-y-2",
  menuItem:
    "flex items-center space-x-3 px-3 py-2 text-secondary-700 hover:bg-primary-50 hover:text-primary-600 rounded-md transition-colors group",
  menuIcon: "text-secondary-400 group-hover:text-primary-500",
  menuLabel: "font-medium",
  footer: "absolute bottom-0 left-0 right-0 p-4 border-t",
  footerText: "text-xs text-secondary-500 text-center",
};

const Sidebar = () => {
  const { t } = useTranslation("common");
  const dispatch = useAppDispatch();
  const { sidebarOpen } = useAppSelector((state: RootLayoutProps) => state.app);

  const menuItems = [
    { id: "home", label: t("navigation.home"), icon: Home, href: "#" },
    {
      id: "dashboard",
      label: t("navigation.dashboard"),
      icon: BarChart3,
      href: "#",
    },
    { id: "profile", label: t("navigation.profile"), icon: User, href: "#" },
    {
      id: "settings",
      label: t("navigation.settings"),
      icon: Settings,
      href: "#",
    },
    { id: "about", label: t("navigation.about"), icon: Info, href: "#" },
  ];

  const handleSidebarToggle = () => {
    dispatch(toggleSidebar());
  };

  if (!sidebarOpen) return null;

  return (
    <>
      {/* Backdrop for mobile */}
      {sidebarOpen && (
        <div
          className={sidebarStyles.backdrop}
          onClick={() => dispatch(setSidebarOpen(false))}
        />
      )}

      {/* Sidebar */}
      <div className={cn(sidebarStyles.container)}>
        {/* Header */}
        {sidebarOpen ? (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleSidebarToggle}
            className={sidebarStyles.closeButton}
          >
            <X size={20} />
          </Button>
        ) : (
          <div className={sidebarStyles.header}>
            <h2 className={sidebarStyles.headerTitle}>
              {t("navigation.menu")}
            </h2>
            <Button
              variant="ghost"
              size="sm"
              className={sidebarStyles.menuButton}
              onClick={handleSidebarToggle}
            >
              <Menu size={20} />
            </Button>
          </div>
        )}

        {/* Navigation */}
        <nav className={sidebarStyles.navigation}>
          <ul className={sidebarStyles.navigationList}>
            {menuItems.map((item) => (
              <li key={item.id}>
                <a href={item.href} className={sidebarStyles.menuItem}>
                  <item.icon size={20} className={sidebarStyles.menuIcon} />
                  <span className={sidebarStyles.menuLabel}>{item.label}</span>
                </a>
              </li>
            ))}
          </ul>
        </nav>

        {/* Footer */}
        <div className={sidebarStyles.footer}>
          <p className={sidebarStyles.footerText}>{t("footer.copyright")}</p>
        </div>
      </div>
    </>
  );
};

export default Sidebar;
