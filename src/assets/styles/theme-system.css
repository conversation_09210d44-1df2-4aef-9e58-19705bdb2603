@theme {
  /* Dynamic Theme Colors - These will be overridden by JavaScript */
  --color-base: oklch(60% 0.1 240);

  --color-surface: oklch(from var(--color-base) calc(l * 1.2) c h);
  --color-brand: oklch(from var(--color-base) calc(l * 3) calc(c * 5) h);
  --color-text: white;
  --color-text-secondary: oklch(from var(--color-text) l c h / 0.6);
  --color-border: oklch(from var(--color-base) calc(l * 1.4) calc(c * 0.5) h);

  /* Radius */
  --radius: 8px;
  --radius-lg: var(--radius);
  --radius-md: calc(var(--radius) - 2px);
  --radius-sm: calc(var(--radius) - 4px);

  /* Tailwind Color Mappings */
  --color-background: var(--color-base);
  --color-foreground: var(--color-text);
  --color-card: var(--color-surface);
  --color-card-foreground: var(--color-text);
  --color-primary: var(--color-brand);
  --color-primary-foreground: var(--color-text);
  --color-secondary: var(--color-surface);
  --color-secondary-foreground: var(--color-text);
  --color-muted: oklch(from var(--color-base) calc(l * 1.1) calc(c * 0.7) h);
  --color-muted-foreground: var(--color-text-secondary);
  --color-accent: var(--color-brand);
  --color-accent-foreground: var(--color-text);

  /* Theme-aware utility colors */
  --color-theme-base: var(--color-base);
  --color-theme-surface: var(--color-surface);
  --color-theme-brand: var(--color-brand);
  --color-theme-text: var(--color-text);
  --color-theme-text-secondary: var(--color-text-secondary);
  --color-theme-border: var(--color-border);
}

/* For non-Tailwind projects - use this approach instead */
/*
:root {
  --color-base: oklch(60% 0.1 240);
  --color-surface: oklch(from var(--color-base) calc(l * 1.2) c h);
  --color-brand: oklch(from var(--color-base) calc(l * 1.5) calc(c * 3) h);
  --color-text: white;
  --color-text-secondary: oklch(from var(--color-text) l c h / 0.6);
  --color-border: oklch(from var(--color-base) calc(l * 1.4) calc(c * 0.5) h);
  --radius: 8px;
  --theme-transition: all 0.3s ease;
}
*/

:root {
  /* Transition settings */
  --default-transition-duration: 300ms;
  --default-transition-timing-function: ease;
  --theme-transition: all var(--default-transition-duration) var(--default-transition-timing-function);

  /* Backwards compatibility aliases */
  --base-color: var(--color-base);
  --surface-color: var(--color-surface);
  --brand-color: var(--color-brand);
  --text-color: var(--color-text);
  --secondary-text-color: var(--color-text-secondary);
  --border-color: var(--color-border);
}

/*
 * Apply base theme to body
 */
body {
  background-color: var(--base-color);
  color: var(--text-color);
  transition: var(--theme-transition);
}

/*
 * Theme Color Utilities
 * Use these classes throughout your application
 */
.bg-base {
  background-color: var(--base-color);
}

.bg-surface {
  background-color: var(--surface-color);
}

.bg-brand {
  background-color: var(--brand-color);
}

.text-primary {
  color: var(--text-color);
}

.text-secondary {
  color: var(--secondary-text-color);
}

.border-custom {
  border-color: var(--border-color);
}

/*
 * Color Picker Component Styles
 */
.color-picker {
  width: 60px;
  height: 40px;
  border-radius: var(--radius);
  border: 2px solid var(--border-color);
  cursor: pointer;
  transition: var(--theme-transition);
  outline: none;
}

.color-picker:hover {
  transform: scale(1.05);
  border-color: var(--brand-color);
  box-shadow: 0 4px 12px oklch(from var(--brand-color) l c h / 0.3);
}

.color-picker:focus {
  outline: 2px solid var(--brand-color);
  outline-offset: 2px;
}

/*
 * Theme Picker Component Styles
 */
.theme-picker {
  background-color: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: calc(var(--radius) * 2);
  padding: 1.5rem;
  transition: var(--theme-transition);
}

.theme-picker-compact {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.theme-picker-header {
  margin-bottom: 1rem;
}

.theme-picker-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 0.5rem 0;
}

.theme-picker-description {
  color: var(--secondary-text-color);
  margin: 0;
  font-size: 0.875rem;
}

.theme-picker-label,
.color-input-label,
.preset-label {
  color: var(--text-color);
  font-weight: 500;
  font-size: 0.875rem;
}

.color-input-group {
  margin-bottom: 1.5rem;
}

.color-input-wrapper {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-top: 0.5rem;
}

.color-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  font-family: monospace;
  font-size: 0.75rem;
}

.color-value {
  color: var(--text-color);
  font-weight: 600;
}

.color-lightness,
.text-mode {
  color: var(--secondary-text-color);
}

.preset-colors {
  margin-bottom: 1.5rem;
}

.preset-label {
  display: block;
  margin-bottom: 0.75rem;
}

.preset-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 0.5rem;
}

.preset-color-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem;
  border: 2px solid transparent;
  border-radius: var(--radius);
  cursor: pointer;
  transition: var(--theme-transition);
  position: relative;
  overflow: hidden;
}

.preset-color-button:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.preset-color-button.preset-active {
  border-color: var(--text-color);
  box-shadow: 0 0 0 2px var(--brand-color);
}

.preset-name {
  color: white;
  font-weight: 500;
  font-size: 0.75rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  z-index: 1;
}

.theme-info-panel {
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
}

.theme-info-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 0.75rem 0;
}

.theme-colors-preview {
  display: flex;
  gap: 1rem;
}

.color-swatch {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: var(--secondary-text-color);
}

.swatch {
  width: 40px;
  height: 40px;
  border-radius: var(--radius);
  border: 1px solid var(--border-color);
  transition: var(--theme-transition);
}

/*
 * Global Smooth Transitions
 * Apply to all elements for smooth theme changes
 */
* {
  transition: background-color 0.3s ease,
    color 0.3s ease,
    border-color 0.3s ease,
    box-shadow 0.3s ease;
}

/*
 * Custom Scrollbar Theming
 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--surface-color);
}

::-webkit-scrollbar-thumb {
  background: var(--brand-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: oklch(from var(--brand-color) calc(l * 0.8) c h);
}

/*
 * Focus Management
 */
:focus-visible {
  outline: 2px solid var(--brand-color);
  outline-offset: 2px;
}

/*
 * Animation Keyframes
 */
@keyframes theme-fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.theme-fade-in {
  animation: theme-fade-in 0.3s ease-out forwards;
}

/*
 * Responsive Design
 */
@media (max-width: 768px) {
  .theme-picker {
    padding: 1rem;
  }

  .color-input-wrapper {
    flex-direction: column;
    align-items: flex-start;
  }

  .preset-grid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  }

  .theme-colors-preview {
    gap: 0.75rem;
  }
}

/*
 * High Contrast Support
 */
@media (prefers-contrast: high) {

  .color-picker,
  .preset-color-button {
    border-width: 3px;
  }

  :focus-visible {
    outline-width: 3px;
  }
}

/*
 * Reduced Motion Support
 */
@media (prefers-reduced-motion: reduce) {
  * {
    transition: none !important;
  }

  .color-picker:hover,
  .preset-color-button:hover {
    transform: none;
  }
}