
// Mock authentication functions for demo purposes
export const mockUsers = [
  {
    id: '1',
    email: '<EMAIL>',
    password: 'admin123',
    name: 'Admin User',
  },
  {
    id: '2',
    email: '<EMAIL>',
    password: 'user123',
    name: 'Regular User',
  },
]

export const authenticateUser = (email: string, password: string) => {
  const user = mockUsers.find(u => u.email === email && u.password === password)
  if (user) {
    const { password: _, ...userWithoutPassword } = user
    return userWithoutPassword
  }
  return null
}

export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export const isValidPassword = (password: string): boolean => {
  return password.length >= 6
}
