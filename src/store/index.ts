
import { configureStore } from '@reduxjs/toolkit'
import { persistStore, persistReducer, FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER } from 'redux-persist'
import storage from 'redux-persist/lib/storage'
import { combineReducers } from '@reduxjs/toolkit'

import authSlice from './slices/authSlice'
import appReducer from './slices/appSlice'
import userReducer from './slices/userSlice'
import notificationReducer from './slices/notificationSlice'

// Persist configuration
const persistConfig = {
  key: 'nakshatracafe-app',
  storage,
  whitelist: ['auth', 'user', 'app'], // Only persist user and app state
  blacklist: ['notification'], // Don't persist notifications
  version: 1,
}

// Combine all reducers
const rootReducer = combineReducers({
  auth: authSlice,
  app: appReducer,
  user: userReducer,
  notification: notificationReducer,
})

// Create persisted reducer
const persistedReducer = persistReducer(persistConfig, rootReducer)

// Configure store
export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
        ignoredActionsPaths: ['register', 'rehydrate'],
        ignoredPaths: ['_persist'],
      },
    }),
  devTools: import.meta.env.DEV,
})

// Create persistor
export const persistor = persistStore(store)

// Export types
export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch

// Export selectors for easy access
export const selectApp = (state: RootState) => state.app
export const selectUser = (state: RootState) => state.user
export const selectNotifications = (state: RootState) => state.notification
