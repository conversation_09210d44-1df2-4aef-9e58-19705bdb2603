import { describe, it, expect, beforeEach } from 'vitest'
import appReducer, {
  setLoading,
  setError,
  clearError,
  updatePreferences,
  setLanguage,
  setTheme,
  toggleNotifications,
  toggleSidebar,
  setSidebarOpen,
  setModalOpen,
  closeModal,
} from '../appSlice'
import type { Language, Theme, UserPreferences } from '@/types'

interface AppState {
  isLoading: boolean
  error: string | null
  preferences: UserPreferences
  sidebarOpen: boolean
  modalOpen: string | null
}

describe('appSlice', () => {
  const initialState: AppState = {
    isLoading: false,
    error: null,
    preferences: {
      theme: 'system',
      language: 'en',
      notifications: true,
    },
    sidebarOpen: false,
    modalOpen: null,
  }

  beforeEach(() => {
    // Reset any mocks or state if needed
  })

  describe('initial state', () => {
    it('should return the initial state', () => {
      expect(appReducer(undefined, { type: 'unknown' })).toEqual(initialState)
    })

    it('should have correct initial values', () => {
      const state = appReducer(undefined, { type: 'unknown' })

      expect(state.isLoading).toBe(false)
      expect(state.error).toBeNull()
      expect(state.preferences.theme).toBe('system')
      expect(state.preferences.language).toBe('en')
      expect(state.preferences.notifications).toBe(true)
      expect(state.sidebarOpen).toBe(false)
      expect(state.modalOpen).toBeNull()
    })
  })

  describe('setLoading reducer', () => {
    it('should set loading to true', () => {
      const action = setLoading(true)
      const state = appReducer(initialState, action)

      expect(state.isLoading).toBe(true)
    })

    it('should set loading to false', () => {
      const previousState = { ...initialState, isLoading: true }
      const action = setLoading(false)
      const state = appReducer(previousState, action)

      expect(state.isLoading).toBe(false)
    })

    it('should not affect other state properties', () => {
      const action = setLoading(true)
      const state = appReducer(initialState, action)

      expect(state.error).toBe(initialState.error)
      expect(state.preferences).toEqual(initialState.preferences)
      expect(state.sidebarOpen).toBe(initialState.sidebarOpen)
      expect(state.modalOpen).toBe(initialState.modalOpen)
    })
  })

  describe('setError reducer', () => {
    it('should set error message', () => {
      const errorMessage = 'Something went wrong'
      const action = setError(errorMessage)
      const state = appReducer(initialState, action)

      expect(state.error).toBe(errorMessage)
    })

    it('should set error to null', () => {
      const previousState = { ...initialState, error: 'Previous error' }
      const action = setError(null)
      const state = appReducer(previousState, action)

      expect(state.error).toBeNull()
    })

    it('should overwrite existing error', () => {
      const previousState = { ...initialState, error: 'Old error' }
      const newError = 'New error'
      const action = setError(newError)
      const state = appReducer(previousState, action)

      expect(state.error).toBe(newError)
    })
  })

  describe('clearError reducer', () => {
    it('should clear existing error', () => {
      const previousState = { ...initialState, error: 'Some error' }
      const action = clearError()
      const state = appReducer(previousState, action)

      expect(state.error).toBeNull()
    })

    it('should not affect state when error is already null', () => {
      const action = clearError()
      const state = appReducer(initialState, action)

      expect(state.error).toBeNull()
      expect(state).toEqual(initialState)
    })
  })

  describe('updatePreferences reducer', () => {
    it('should update single preference property', () => {
      const action = updatePreferences({ theme: 'dark' })
      const state = appReducer(initialState, action)

      expect(state.preferences.theme).toBe('dark')
      expect(state.preferences.language).toBe('en')
      expect(state.preferences.notifications).toBe(true)
    })

    it('should update multiple preference properties', () => {
      const updates = { theme: 'light' as Theme, notifications: false }
      const action = updatePreferences(updates)
      const state = appReducer(initialState, action)

      expect(state.preferences.theme).toBe('light')
      expect(state.preferences.notifications).toBe(false)
      expect(state.preferences.language).toBe('en')
    })

    it('should merge with existing preferences', () => {
      const previousState = {
        ...initialState,
        preferences: { theme: 'dark' as Theme, language: 'hi' as Language, notifications: false }
      }
      const action = updatePreferences({ language: 'mr' })
      const state = appReducer(previousState, action)

      expect(state.preferences.theme).toBe('dark')
      expect(state.preferences.language).toBe('mr')
      expect(state.preferences.notifications).toBe(false)
    })

    it('should handle empty update object', () => {
      const action = updatePreferences({})
      const state = appReducer(initialState, action)

      expect(state.preferences).toEqual(initialState.preferences)
    })
  })

  describe('setLanguage reducer', () => {
    it('should set language to Hindi', () => {
      const action = setLanguage('hi')
      const state = appReducer(initialState, action)

      expect(state.preferences.language).toBe('hi')
    })

    it('should set language to Marathi', () => {
      const action = setLanguage('mr')
      const state = appReducer(initialState, action)

      expect(state.preferences.language).toBe('mr')
    })

    it('should change language from one to another', () => {
      const previousState = {
        ...initialState,
        preferences: { ...initialState.preferences, language: 'hi' as Language }
      }
      const action = setLanguage('en')
      const state = appReducer(previousState, action)

      expect(state.preferences.language).toBe('en')
    })

    it('should not affect other preferences', () => {
      const action = setLanguage('hi')
      const state = appReducer(initialState, action)

      expect(state.preferences.theme).toBe(initialState.preferences.theme)
      expect(state.preferences.notifications).toBe(initialState.preferences.notifications)
    })
  })

  describe('setTheme reducer', () => {
    it('should set theme to light', () => {
      const action = setTheme('light')
      const state = appReducer(initialState, action)

      expect(state.preferences.theme).toBe('light')
    })

    it('should set theme to dark', () => {
      const action = setTheme('dark')
      const state = appReducer(initialState, action)

      expect(state.preferences.theme).toBe('dark')
    })

    it('should set theme to system', () => {
      const previousState = {
        ...initialState,
        preferences: { ...initialState.preferences, theme: 'light' as Theme }
      }
      const action = setTheme('system')
      const state = appReducer(previousState, action)

      expect(state.preferences.theme).toBe('system')
    })

    it('should not affect other preferences', () => {
      const action = setTheme('dark')
      const state = appReducer(initialState, action)

      expect(state.preferences.language).toBe(initialState.preferences.language)
      expect(state.preferences.notifications).toBe(initialState.preferences.notifications)
    })
  })

  describe('toggleNotifications reducer', () => {
    it('should toggle notifications from true to false', () => {
      const action = toggleNotifications()
      const state = appReducer(initialState, action)

      expect(state.preferences.notifications).toBe(false)
    })

    it('should toggle notifications from false to true', () => {
      const previousState = {
        ...initialState,
        preferences: { ...initialState.preferences, notifications: false }
      }
      const action = toggleNotifications()
      const state = appReducer(previousState, action)

      expect(state.preferences.notifications).toBe(true)
    })

    it('should not affect other preferences', () => {
      const action = toggleNotifications()
      const state = appReducer(initialState, action)

      expect(state.preferences.theme).toBe(initialState.preferences.theme)
      expect(state.preferences.language).toBe(initialState.preferences.language)
    })
  })

  describe('toggleSidebar reducer', () => {
    it('should toggle sidebar from false to true', () => {
      const action = toggleSidebar()
      const state = appReducer(initialState, action)

      expect(state.sidebarOpen).toBe(true)
    })

    it('should toggle sidebar from true to false', () => {
      const previousState = { ...initialState, sidebarOpen: true }
      const action = toggleSidebar()
      const state = appReducer(previousState, action)

      expect(state.sidebarOpen).toBe(false)
    })
  })

  describe('setSidebarOpen reducer', () => {
    it('should set sidebar to open', () => {
      const action = setSidebarOpen(true)
      const state = appReducer(initialState, action)

      expect(state.sidebarOpen).toBe(true)
    })

    it('should set sidebar to closed', () => {
      const previousState = { ...initialState, sidebarOpen: true }
      const action = setSidebarOpen(false)
      const state = appReducer(previousState, action)

      expect(state.sidebarOpen).toBe(false)
    })
  })

  describe('setModalOpen reducer', () => {
    it('should set modal to open with specific modal name', () => {
      const modalName = 'user-profile'
      const action = setModalOpen(modalName)
      const state = appReducer(initialState, action)

      expect(state.modalOpen).toBe(modalName)
    })

    it('should set modal to null', () => {
      const previousState = { ...initialState, modalOpen: 'some-modal' }
      const action = setModalOpen(null)
      const state = appReducer(previousState, action)

      expect(state.modalOpen).toBeNull()
    })

    it('should change from one modal to another', () => {
      const previousState = { ...initialState, modalOpen: 'modal-1' }
      const action = setModalOpen('modal-2')
      const state = appReducer(previousState, action)

      expect(state.modalOpen).toBe('modal-2')
    })
  })

  describe('closeModal reducer', () => {
    it('should close open modal', () => {
      const previousState = { ...initialState, modalOpen: 'some-modal' }
      const action = closeModal()
      const state = appReducer(previousState, action)

      expect(state.modalOpen).toBeNull()
    })

    it('should not affect state when modal is already closed', () => {
      const action = closeModal()
      const state = appReducer(initialState, action)

      expect(state.modalOpen).toBeNull()
      expect(state).toEqual(initialState)
    })
  })

  describe('action creators', () => {
    it('should create setLoading action with correct type and payload', () => {
      const action = setLoading(true)

      expect(action.type).toBe('app/setLoading')
      expect(action.payload).toBe(true)
    })

    it('should create setError action with correct type and payload', () => {
      const errorMessage = 'Test error'
      const action = setError(errorMessage)

      expect(action.type).toBe('app/setError')
      expect(action.payload).toBe(errorMessage)
    })

    it('should create clearError action with correct type', () => {
      const action = clearError()

      expect(action.type).toBe('app/clearError')
      expect(action.payload).toBeUndefined()
    })

    it('should create updatePreferences action with correct type and payload', () => {
      const preferences = { theme: 'dark' as Theme }
      const action = updatePreferences(preferences)

      expect(action.type).toBe('app/updatePreferences')
      expect(action.payload).toEqual(preferences)
    })

    it('should create setLanguage action with correct type and payload', () => {
      const language: Language = 'hi'
      const action = setLanguage(language)

      expect(action.type).toBe('app/setLanguage')
      expect(action.payload).toBe(language)
    })

    it('should create setTheme action with correct type and payload', () => {
      const theme: Theme = 'light'
      const action = setTheme(theme)

      expect(action.type).toBe('app/setTheme')
      expect(action.payload).toBe(theme)
    })

    it('should create toggleNotifications action with correct type', () => {
      const action = toggleNotifications()

      expect(action.type).toBe('app/toggleNotifications')
      expect(action.payload).toBeUndefined()
    })

    it('should create toggleSidebar action with correct type', () => {
      const action = toggleSidebar()

      expect(action.type).toBe('app/toggleSidebar')
      expect(action.payload).toBeUndefined()
    })

    it('should create setSidebarOpen action with correct type and payload', () => {
      const action = setSidebarOpen(true)

      expect(action.type).toBe('app/setSidebarOpen')
      expect(action.payload).toBe(true)
    })

    it('should create setModalOpen action with correct type and payload', () => {
      const modalName = 'test-modal'
      const action = setModalOpen(modalName)

      expect(action.type).toBe('app/setModalOpen')
      expect(action.payload).toBe(modalName)
    })

    it('should create closeModal action with correct type', () => {
      const action = closeModal()

      expect(action.type).toBe('app/closeModal')
      expect(action.payload).toBeUndefined()
    })
  })

  describe('edge cases and error scenarios', () => {
    it('should handle undefined payload gracefully for setError', () => {
      const action = { type: 'app/setError', payload: undefined }
      const state = appReducer(initialState, action)

      expect(state.error).toBeUndefined()
    })

    it('should handle empty string as error message', () => {
      const action = setError('')
      const state = appReducer(initialState, action)

      expect(state.error).toBe('')
    })

    it('should handle empty string as modal name', () => {
      const action = setModalOpen('')
      const state = appReducer(initialState, action)

      expect(state.modalOpen).toBe('')
    })

    it('should maintain immutability - original state should not be mutated', () => {
      const originalState = { ...initialState }
      const action = setLoading(true)

      appReducer(initialState, action)

      expect(initialState).toEqual(originalState)
    })

    it('should maintain immutability for nested preferences object', () => {
      const originalPreferences = { ...initialState.preferences }
      const action = setTheme('dark')

      appReducer(initialState, action)

      expect(initialState.preferences).toEqual(originalPreferences)
    })

    it('should handle multiple rapid state changes correctly', () => {
      let state = initialState

      state = appReducer(state, setLoading(true))
      state = appReducer(state, setError('Error occurred'))
      state = appReducer(state, setTheme('dark'))
      state = appReducer(state, toggleSidebar())
      state = appReducer(state, setModalOpen('test-modal'))

      expect(state.isLoading).toBe(true)
      expect(state.error).toBe('Error occurred')
      expect(state.preferences.theme).toBe('dark')
      expect(state.sidebarOpen).toBe(true)
      expect(state.modalOpen).toBe('test-modal')
    })

    it('should handle complex preference updates', () => {
      const complexUpdate = {
        theme: 'light' as Theme,
        language: 'mr' as Language,
        notifications: false
      }
      const action = updatePreferences(complexUpdate)
      const state = appReducer(initialState, action)

      expect(state.preferences).toEqual(complexUpdate)
    })
  })

  describe('state consistency', () => {
    it('should maintain consistent state structure after all operations', () => {
      let state = initialState

      // Apply various operations
      state = appReducer(state, setLoading(true))
      state = appReducer(state, setError('Test error'))
      state = appReducer(state, updatePreferences({ theme: 'dark', notifications: false }))
      state = appReducer(state, toggleSidebar())
      state = appReducer(state, setModalOpen('profile'))

      // Verify state structure is maintained
      expect(state).toHaveProperty('isLoading')
      expect(state).toHaveProperty('error')
      expect(state).toHaveProperty('preferences')
      expect(state).toHaveProperty('sidebarOpen')
      expect(state).toHaveProperty('modalOpen')
      expect(state.preferences).toHaveProperty('theme')
      expect(state.preferences).toHaveProperty('language')
      expect(state.preferences).toHaveProperty('notifications')
    })

    it('should handle clearing and setting operations in sequence', () => {
      let state = initialState

      // Set error, then clear it
      state = appReducer(state, setError('Initial error'))
      expect(state.error).toBe('Initial error')

      state = appReducer(state, clearError())
      expect(state.error).toBeNull()

      // Open modal, then close it
      state = appReducer(state, setModalOpen('test-modal'))
      expect(state.modalOpen).toBe('test-modal')

      state = appReducer(state, closeModal())
      expect(state.modalOpen).toBeNull()

      // Toggle sidebar multiple times
      state = appReducer(state, toggleSidebar())
      expect(state.sidebarOpen).toBe(true)

      state = appReducer(state, toggleSidebar())
      expect(state.sidebarOpen).toBe(false)
    })
  })
})
