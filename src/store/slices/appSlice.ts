
import { createSlice, type PayloadAction } from '@reduxjs/toolkit'
import type { Language, UserPreferences } from '@/types'

interface AppState {
  isLoading: boolean
  error: string | null
  preferences: UserPreferences
  sidebarOpen: boolean
  modalOpen: string | null
}

const initialState: AppState = {
  isLoading: false,
  error: null,
  preferences: {
    theme: 'system',
    language: 'en',
    notifications: true,
  },
  sidebarOpen: false,
  modalOpen: null,
}

const appSlice = createSlice({
  name: 'app',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload
    },
    clearError: (state) => {
      state.error = null
    },
    updatePreferences: (state, action: PayloadAction<Partial<UserPreferences>>) => {
      state.preferences = { ...state.preferences, ...action.payload }
    },
    setLanguage: (state, action: PayloadAction<Language>) => {
      state.preferences.language = action.payload
    },
    setTheme: (state, action: PayloadAction<'light' | 'dark' | 'system'>) => {
      state.preferences.theme = action.payload
    },
    toggleNotifications: (state) => {
      state.preferences.notifications = !state.preferences.notifications
    },
    toggleSidebar: (state) => {
      state.sidebarOpen = !state.sidebarOpen
    },
    setSidebarOpen: (state, action: PayloadAction<boolean>) => {
      state.sidebarOpen = action.payload
    },
    setModalOpen: (state, action: PayloadAction<string | null>) => {
      state.modalOpen = action.payload
    },
    closeModal: (state) => {
      state.modalOpen = null
    },
  },
})

export const {
  setLoading,
  setError,
  clearError,
  updatePreferences,
  setLanguage,
  setTheme,
  toggleNotifications,
  toggleSidebar,
  setSidebarOpen,
  setModalOpen,
  closeModal,
} = appSlice.actions

export default appSlice.reducer
