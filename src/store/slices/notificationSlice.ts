
import { createSlice, type PayloadAction } from '@reduxjs/toolkit'
import type { NotificationState } from '@/types'

interface NotificationSliceState {
  notifications: NotificationState[]
}

const initialState: NotificationSliceState = {
  notifications: [],
}

const notificationSlice = createSlice({
  name: 'notification',
  initialState,
  reducers: {
    addNotification: (state, action: PayloadAction<Omit<NotificationState, 'id' | 'timestamp'>>) => {
      const notification: NotificationState = {
        ...action.payload,
        id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        timestamp: Date.now(),
      }
      state.notifications.push(notification)
    },
    removeNotification: (state, action: PayloadAction<string>) => {
      state.notifications = state.notifications.filter(
        notification => notification.id !== action.payload
      )
    },
    clearNotifications: (state) => {
      state.notifications = []
    },
    clearNotificationsByType: (state, action: PayloadAction<NotificationState['type']>) => {
      state.notifications = state.notifications.filter(
        notification => notification.type !== action.payload
      )
    },
  },
})

export const {
  addNotification,
  removeNotification,
  clearNotifications,
  clearNotificationsByType,
} = notificationSlice.actions

export default notificationSlice.reducer

// Helper action creators
export const showSuccessNotification = (message: string, title = 'Success') =>
  addNotification({ type: 'success', title, message, duration: 5000 })

export const showErrorNotification = (message: string, title = 'Error') =>
  addNotification({ type: 'error', title, message, duration: 7000 })

export const showWarningNotification = (message: string, title = 'Warning') =>
  addNotification({ type: 'warning', title, message, duration: 6000 })

export const showInfoNotification = (message: string, title = 'Info') =>
  addNotification({ type: 'info', title, message, duration: 4000 })
