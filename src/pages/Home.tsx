import { useTranslation } from "react-i18next";
import { <PERSON><PERSON><PERSON>, TrendingUp, Users, Activity } from "lucide-react";

const Home = () => {
  const { t } = useTranslation("common");

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">{t("home.title")}</h1>
      <p className="text-lg text-secondary-600 mb-8">{t("home.description")}</p>
    </div>
  );
};

export default Home;
