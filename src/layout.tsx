import { PersistGate } from "redux-persist/integration/react";

// Redux
import { persistor } from "@/store";

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <PersistGate
      loading={
        <div className="min-h-screen bg-secondary-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
            <p className="text-secondary-600">Loading...</p>
          </div>
        </div>
      }
      persistor={persistor}
    >
      <div className="min-h-screen p-8">{children}</div>
    </PersistGate>
  );
}
