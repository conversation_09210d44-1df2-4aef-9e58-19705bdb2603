
import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'
import LanguageDetector from 'i18next-browser-languagedetector'

// Import translations
import enTranslations from './locales/en/common.json'
import hiTranslations from './locales/hi/common.json'
import mrTranslations from './locales/mr/common.json'

// Language resources
const resources = {
  en: {
    common: enTranslations,
  },
  hi: {
    common: hiTranslations,
  },
  mr: {
    common: mrTranslations,
  },
}

// i18n configuration
i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    lng: 'en', // default language
    fallbackLng: 'en',
    debug: import.meta.env.DEV,

    // Namespace configuration
    defaultNS: 'common',
    ns: ['common'],

    // Interpolation options
    interpolation: {
      escapeValue: false, // React already escapes values
    },

    // Language detector options
    detection: {
      order: ['localStorage', 'navigator', 'htmlTag'],
      caches: ['localStorage'],
      lookupLocalStorage: 'vite-react-app-language',
    },

    // React i18next options
    react: {
      useSuspense: false,
    },
  })

export default i18n

// Language configuration
export const LANGUAGES = [
  {
    code: 'en' as const,
    name: 'English',
    nativeName: 'English',
    flag: '🇺🇸',
  },
  {
    code: 'hi' as const,
    name: 'Hindi',
    nativeName: 'हिन्दी',
    flag: '🇮🇳',
  },
  {
    code: 'mr' as const,
    name: 'Marathi',
    nativeName: 'मराठी',
    flag: '🇮🇳',
  },
]

export type SupportedLanguage = typeof LANGUAGES[number]['code']
