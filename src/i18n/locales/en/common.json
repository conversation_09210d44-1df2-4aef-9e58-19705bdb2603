{"app": {"title": "Nakshatra Cafe", "description": "Nakshatra Cafe is a Vedic astrology application", "welcome": "Welcome to Nakshatra Cafe App!", "loading": "Loading...", "error": "Something went wrong", "tryAgain": "Try again", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "submit": "Submit", "reset": "Reset", "search": "Search", "filter": "Filter", "sort": "Sort", "refresh": "Refresh"}, "navigation": {"menu": "<PERSON><PERSON>", "home": "Home", "profile": "Profile", "settings": "Settings", "about": "About", "contact": "Contact", "dashboard": "Dashboard", "logout": "Logout", "login": "<PERSON><PERSON>", "register": "Register"}, "user": {"profile": "Profile", "name": "Name", "email": "Email", "avatar": "Avatar", "preferences": "Preferences", "updateProfile": "Update Profile", "changePassword": "Change Password", "accountSettings": "Account <PERSON><PERSON>"}, "settings": {"general": "General", "appearance": "Appearance", "language": "Language", "theme": "Theme", "notifications": "Notifications", "privacy": "Privacy", "security": "Security", "lightMode": "Light Mode", "darkMode": "Dark Mode", "systemMode": "System Mode", "enableNotifications": "Enable Notifications", "disableNotifications": "Disable Notifications"}, "language": {"selectLanguage": "Select Language", "changeLanguage": "Change Language", "currentLanguage": "Current Language", "english": "English", "hindi": "Hindi", "marathi": "Marathi"}, "notifications": {"success": "Success", "error": "Error", "warning": "Warning", "info": "Information", "profileUpdated": "Profile updated successfully", "settingsSaved": "Setting<PERSON> saved successfully", "languageChanged": "Language changed to {{language}}", "themeChanged": "Theme changed to {{theme}}", "notificationToggled": "Notifications {{status}}", "enabled": "enabled", "disabled": "disabled"}, "forms": {"validation": {"required": "This field is required", "email": "Please enter a valid email address", "minLength": "Minimum {{count}} characters required", "maxLength": "Maximum {{count}} characters allowed", "passwordMismatch": "Passwords do not match"}, "placeholders": {"name": "Enter your name", "email": "Enter your email", "password": "Enter your password", "confirmPassword": "Confirm your password", "search": "Search..."}}, "demo": {"title": "Demo Components", "description": "This section demonstrates various features of the application", "counter": "Counter", "increment": "Increment", "decrement": "Decrement", "reset": "Reset", "currentValue": "Current value: {{value}}", "todoList": "Todo List", "addTodo": "Add <PERSON>", "todoPlaceholder": "Enter a new todo...", "noTodos": "No todos yet. Add one above!", "completeTodo": "Mark as complete", "deleteTodo": "Delete todo", "weatherWidget": "Weather Widget", "temperature": "Temperature", "humidity": "<PERSON><PERSON><PERSON><PERSON>", "windSpeed": "Wind Speed", "condition": "Condition"}, "footer": {"copyright": "Nakshatra Cafe. All rights reserved.", "madeWith": "Made with ❤️ using Vite, React, TypeScript, and Tailwind CSS"}}