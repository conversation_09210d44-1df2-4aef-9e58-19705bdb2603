
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useLocalStorage } from '../useLocalStorage'

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
  writable: true,
})

describe('useLocalStorage Hook', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    localStorageMock.getItem.mockReturnValue(null)
  })

  it('returns initial value when localStorage is empty', () => {
    const { result } = renderHook(() => useLocalStorage('test-key', 'initial'))
    
    expect(result.current[0]).toBe('initial')
    expect(localStorageMock.getItem).toHaveBeenCalledWith('test-key')
  })

  it('returns value from localStorage when available', () => {
    localStorageMock.getItem.mockReturnValue('"stored-value"')
    
    const { result } = renderHook(() => useLocalStorage('test-key', 'initial'))
    
    expect(result.current[0]).toBe('stored-value')
    expect(localStorageMock.getItem).toHaveBeenCalledWith('test-key')
  })

  it('saves value to localStorage when setValue is called', () => {
    const { result } = renderHook(() => useLocalStorage('test-key', 'initial'))
    
    act(() => {
      result.current[1]('new-value')
    })
    
    expect(result.current[0]).toBe('new-value')
    expect(localStorageMock.setItem).toHaveBeenCalledWith('test-key', '"new-value"')
  })

  it('handles function updates correctly', () => {
    localStorageMock.getItem.mockReturnValue('5')
    
    const { result } = renderHook(() => useLocalStorage('counter', 0))
    
    act(() => {
      result.current[1]((prev) => prev + 1)
    })
    
    expect(result.current[0]).toBe(6) // 5 + 1
    expect(localStorageMock.setItem).toHaveBeenCalledWith('counter', '6')
  })

  it('removes value from localStorage when removeValue is called', () => {
    const { result } = renderHook(() => useLocalStorage('test-key', 'initial'))
    
    act(() => {
      result.current[2]() // removeValue
    })
    
    expect(result.current[0]).toBe('initial')
    expect(localStorageMock.removeItem).toHaveBeenCalledWith('test-key')
  })

  it('handles JSON parsing errors gracefully', () => {
    localStorageMock.getItem.mockReturnValue('invalid-json{')
    const consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})
    
    const { result } = renderHook(() => useLocalStorage('test-key', 'fallback'))
    
    expect(result.current[0]).toBe('fallback')
    expect(consoleWarnSpy).toHaveBeenCalled()
    
    consoleWarnSpy.mockRestore()
  })

  it('handles localStorage setItem errors gracefully', () => {
    localStorageMock.setItem.mockImplementation(() => {
      throw new Error('localStorage is full')
    })
    const consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})
    
    const { result } = renderHook(() => useLocalStorage('test-key', 'initial'))
    
    act(() => {
      result.current[1]('new-value')
    })
    
    expect(consoleWarnSpy).toHaveBeenCalled()
    consoleWarnSpy.mockRestore()
  })

  it('works with different data types', () => {
    // Test with object
    const { result: objectResult } = renderHook(() => 
      useLocalStorage('object-key', { name: 'test', age: 25 })
    )
    
    act(() => {
      objectResult.current[1]({ name: 'updated', age: 30 })
    })
    
    expect(objectResult.current[0]).toEqual({ name: 'updated', age: 30 })
    
    // Test with array
    const { result: arrayResult } = renderHook(() => 
      useLocalStorage('array-key', [1, 2, 3])
    )
    
    act(() => {
      arrayResult.current[1]([4, 5, 6])
    })
    
    expect(arrayResult.current[0]).toEqual([4, 5, 6])
  })
})
