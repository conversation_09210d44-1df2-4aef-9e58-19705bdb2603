
import { useDispatch, useSelector, type TypedUseSelectorHook } from 'react-redux'
import type { RootState, AppDispatch } from '@/store'

// Typed hooks for Redux
export const useAppDispatch = () => useDispatch<AppDispatch>()
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector

// Convenience selectors
export const useAppState = () => useAppSelector(state => state.app)
export const useUserState = () => useAppSelector(state => state.user)
export const useNotifications = () => useAppSelector(state => state.notification.notifications)
