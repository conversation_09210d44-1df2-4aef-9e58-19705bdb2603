
import { useAppDispatch } from './redux'
import {
  addNotification,
  removeNotification,
  clearNotifications,
  showSuccessNotification,
  showErrorNotification,
  showWarningNotification,
  showInfoNotification,
} from '@/store/slices/notificationSlice'

/**
 * Custom hook for managing notifications
 */
export function useNotification() {
  const dispatch = useAppDispatch()

  return {
    // Basic notification actions
    add: (notification: Parameters<typeof addNotification>[0]) =>
      dispatch(addNotification(notification)),
    remove: (id: string) => dispatch(removeNotification(id)),
    clear: () => dispatch(clearNotifications()),

    // Convenience methods for different types
    success: (message: string, title?: string) =>
      dispatch(showSuccessNotification(message, title)),
    error: (message: string, title?: string) =>
      dispatch(showErrorNotification(message, title)),
    warning: (message: string, title?: string) =>
      dispatch(showWarningNotification(message, title)),
    info: (message: string, title?: string) =>
      dispatch(showInfoNotification(message, title)),
  }
}
