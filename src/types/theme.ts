
/**
 * Type definitions for the Dynamic Theme Picker system
 */

export interface ThemeColors {
  /** The base/background color in hex format */
  baseColor: string;
  /** Calculated lightness value (0-100) */
  lightness: number;
  /** Whether to use dark text (true) or light text (false) */
  isDarkText: boolean;
}

export interface ThemeConfig {
  /** Initial theme color */
  initialColor?: string;
  /** Custom color presets */
  presets?: ColorPreset[];
  /** Enable smooth transitions */
  enableTransitions?: boolean;
  /** Custom color relationships */
  colorRelations?: ColorRelations;
}

export interface ColorPreset {
  name: string;
  color: string;
  description?: string;
}

export interface ColorRelations {
  /** Surface color multiplier for lightness (default: 1.2) */
  surfaceLightness?: number;
  /** Brand color lightness multiplier (default: 1.5) */
  brandLightness?: number;
  /** Brand color chroma multiplier (default: 3) */
  brandChroma?: number;
  /** Border color lightness multiplier (default: 1.4) */
  borderLightness?: number;
  /** Border color chroma multiplier (default: 0.5) */
  borderChroma?: number;
}

export interface ThemePickerProps {
  /** Current theme colors */
  colors: ThemeColors;
  /** Callback when theme changes */
  onColorChange: (color: string) => void;
  /** Optional label for the color picker */
  label?: string;
  /** CSS class name for custom styling */
  className?: string;
  /** Show preset color buttons */
  showPresets?: boolean;
  /** Custom color presets */
  presets?: ColorPreset[];
  /** Enable compact mode */
  compact?: boolean;
}

export type ThemeChangeCallback = (colors: ThemeColors) => void;
