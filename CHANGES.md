```
bun i @reduxjs/toolkit @types/redux-persist class-variance-authority clsx i18next i18next-browser-languagedetector lucide-react react-dom react-i18next react-redux redux-persist
```

```
bun i -d @testing-library/jest-dom @testing-library/react @testing-library/user-event @types/node @types/react @types/react-dom @vitest/ui eslint-plugin-testing-library jsdom prettier vitest
```

```
bunx --bun shadcn@latest init
```

```
bunx --bun shadcn@latest add button
```
